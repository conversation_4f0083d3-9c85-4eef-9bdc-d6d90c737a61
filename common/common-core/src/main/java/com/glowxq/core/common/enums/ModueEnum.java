package com.glowxq.core.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/11 0:24
 */
@Getter
@AllArgsConstructor
public enum ModueEnum implements BaseType{
    SYSTEM("system","系统管理"),
    OJDATA("ojdata","Oj数据"),
    BASECONFIG("baseConfig","基础配置"),
    GLOWC("glowc","GLOWC"),
    JUDGE("judge","测评"),
    CLASS("class","班级"),
    CONTEST("contest","比赛"),
    QUESTION("question","题目"),
    COURSE("course","课程"),
    ASSIGNMENT("assignment","作业"),
    CODE("code","代码"),
    ;
    /**
     * CODE
     */
    private final String code;

    /**
     * 模块名称
     */
    private final String name;

    /**
     * 匹配模块枚举中文 ，若未匹配上则返回当前入参的code
     * @param module 枚举code
     * @return 模块名称
     */
    public static String match(String module) {
        for (ModueEnum value : ModueEnum.values()) {
            if (value.getCode().equals(module)) {
                return value.getName();
            }
        }
        return module;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
