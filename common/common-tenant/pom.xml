<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.glowxq</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-tenant</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.glowxq</groupId>
            <artifactId>common-core</artifactId>
            <version>${revision}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.glowxq</groupId>
            <artifactId>common-db-redis</artifactId>
            <version>${revision}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.mybatis-flex</groupId>
            <artifactId>mybatis-flex-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mybatis-flex</groupId>
            <artifactId>mybatis-flex-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.glowxq</groupId>
            <artifactId>common-security</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.glowxq.excel</groupId>
            <artifactId>common-excel</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>
</project>