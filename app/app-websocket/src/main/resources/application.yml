server:
  port: 9993
  shutdown: graceful

spring:
  threads:
    virtual:
      # 启用虚拟线程
      enabled: true
  profiles:
    active: local
  config:
    import:
      - optional:classpath:/config/${spring.profiles.active}/flyway.yml
      - optional:classpath:/config/${spring.profiles.active}/knife4j.yml
      - optional:classpath:/config/${spring.profiles.active}/oss.yml
      - optional:classpath:/config/${spring.profiles.active}/mybatis-flex.yml
      - optional:classpath:/config/${spring.profiles.active}/mysql.yml
      - optional:classpath:/config/${spring.profiles.active}/page-helper.yml
      - optional:classpath:/config/${spring.profiles.active}/redis.yml
      - optional:classpath:/config/${spring.profiles.active}/sa-token.yml
      - optional:classpath:/config/${spring.profiles.active}/feishu.yml
  application:
    name: websocket-service

app:
  version: @project.version@
  name: ${spring.application.name}
  environment: ${spring.profiles.active}
  business: websocket
