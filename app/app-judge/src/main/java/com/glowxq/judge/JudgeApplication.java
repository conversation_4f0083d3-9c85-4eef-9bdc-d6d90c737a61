package com.glowxq.judge;

import com.glowxq.core.common.constant.Constant;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@ComponentScan(basePackages = {Constant.BASE_PACKAGE})
@EnableAspectJAutoProxy
@RequiredArgsConstructor
public class JudgeApplication {

    @Getter
    private static String version;

    @Value("${app.version}")
    private String appVersion;

    private static void setVersion(String appVersion) {
        JudgeApplication.version = appVersion;
    }

    public static void main(String[] args) {
        SpringApplication.run(JudgeApplication.class, args);
        String template = """
                             ___      _                             __ _                       (_)              (_)      _             __ _         \s
                            / __|    | |     ___   __ __ __ __ __  / _` |     o O O   ___      | |     o O O    | |   __| |   _  _    / _` |   ___  \s
                           | (_ |    | |    / _ \\  \\ V  V / \\ \\ /  \\__, |    o       / _ \\    _/ |    o        _/ |  / _` |  | +| |   \\__, |  / -_) \s
                            \\___|   _|_|_   \\___/   \\_/\\_/  /_\\_\\   __|_|   TS__[O]  \\___/   |__/_   TS__[O]  |__/_  \\__,_|   \\_,_|   |___/   \\___| \s
                          _|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""| {======|_|""\"""|_|""\"""| {======|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|\s
                          "`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'./o--000'"`-0-0-'"`-0-0-'./o--000'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'\s
                                                    ------------------%s  (v%s)-------------------
                          """;
        String result = String.format(template, "glowxq-oj-judge", getVersion());
        System.out.println(result);
    }

    @PostConstruct
    public void init() {
        setVersion(appVersion); // 通过辅助方法设置静态字段
    }
}