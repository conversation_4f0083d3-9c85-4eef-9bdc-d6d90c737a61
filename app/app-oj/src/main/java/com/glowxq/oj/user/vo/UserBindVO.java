package com.glowxq.oj.user.vo;

import com.glowxq.oj.code.pojo.po.CodeRecord;
import com.glowxq.oj.group.pojo.po.Group;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.glowxq.oj.system.bo.OjUserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBindVO {

    /**
     * ac题目数量
     */
    private OjUserInfo ojUserInfo;

    private List<MetaTag> tags;

    private List<Group> groups;

    private List<CodeRecord> codeRecords;

    public UserBindVO(List<MetaTag> metaTagList, List<Group> groupList) {
        this.tags = metaTagList;
        this.groups = groupList;
    }
}
