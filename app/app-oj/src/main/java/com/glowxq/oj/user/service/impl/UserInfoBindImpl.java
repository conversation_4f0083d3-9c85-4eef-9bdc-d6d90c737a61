package com.glowxq.oj.user.service.impl;

import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.oj.group.pojo.po.Group;
import com.glowxq.oj.group.service.GroupService;
import com.glowxq.oj.user.pojo.po.UserInfo;
import com.glowxq.oj.user.service.UserInfoBind;
import com.glowxq.oj.user.service.UserInfoService;
import com.glowxq.system.admin.pojo.OJUserInfoBO;
import com.glowxq.system.meta.enums.TagBusinessBindType;
import com.glowxq.system.meta.service.MetaTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/28
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Primary
public class UserInfoBindImpl implements UserInfoBind {

    private final MetaTagService metaTagService;

    private final GroupService groupService;

    private final UserInfoService userInfoService;

    @Override
    public void bindTags(Long userId, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        List<Long> tagIdList = tagIds.stream().distinct().toList();
        metaTagService.bindTags(userId, tagIdList, TagBusinessBindType.User);
    }

    @Override
    public void bindGroups(Long userId, List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        List<Long> groupIdList = groupIds.stream().distinct().toList();
        groupService.bindGroups(userId, groupIdList, TagBusinessBindType.User);
    }

    @Override
    public void updateTags(Long userId, List<Long> tagIds) {
        metaTagService.unBindAll(userId, TagBusinessBindType.User);
        bindTags(userId, tagIds);
    }

    @Override
    public void updateGroups(Long userId, List<Long> groupIds) {
        groupService.unBindAll(userId, TagBusinessBindType.User);
        this.bindGroups(userId, groupIds);
    }

    @Override
    public List<Long> listUserIdByTag(List<Long> tagIds) {
        return metaTagService.listBusinessIdByTagIds(tagIds, TagBusinessBindType.User);
    }

    @Override
    public List<Long> listUserIdByGroup(List<Long> groupIds) {
        return groupService.listBusinessIdByGroupIds(groupIds, TagBusinessBindType.User);
    }

    @Override
    public Set<Long> listGroupIdByUserId(Long userId) {
        List<Group> groups = groupService.listByBusinessId(userId, TagBusinessBindType.User);
        return groups.stream().map(Group::getId).collect(Collectors.toSet());
    }

    @Override
    public void bindUserInfo(Long id, OJUserInfoBO userInfoBo) {
        UserInfo userInfo = userInfoService.autoByUserId(id, userInfoBo.getTenantId());
        userInfo.setName(userInfoBo.getName());
        userInfo.setAcNum(userInfoBo.getAcNum());
        userInfo.setIntegral(userInfoBo.getIntegral());
        userInfo.setContinuousSignDay(userInfoBo.getContinuousSignDay());
        userInfo.setSubmitNum(userInfoBo.getSubmitNum());
        userInfo.setTitle(userInfoBo.getTitle());
        userInfo.setColor(userInfoBo.getColor());
        userInfo.setTenantId(userInfoBo.getTenantId());
        userInfoService.updateById(userInfo);
    }

    @Override
    public OJUserInfoBO getByOjUserId(Long userId) {
        UserInfo userInfo = userInfoService.autoByUserId(userId, null);
        return BeanCopyUtils.copy(userInfo, OJUserInfoBO.class);
    }
}
