FROM ubuntu:18.04

ARG DEBIAN_FRONTEND=noninteractive

ENV TZ=Asia/Shanghai

# 安装需要的编译器和运行时依赖
# gcc 用于编译和运行 C C++ 代码，python3 用于运行 Python 代码，wget和tar用于安装Java
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    python3 \
    wget \
    tar \
    && rm -rf /var/lib/apt/lists/*

# 将本地Java包复制到镜像中
COPY ./app/app-oj/openjdk-21.0.2_linux-x64_bin.tar.gz /tmp/openjdk-21.tar.gz

# 安装Java 21
RUN mkdir -p /usr/local/java \
    && tar -xzf /tmp/openjdk-21.tar.gz -C /usr/local/java --strip-components 1 \
    && rm -f /tmp/openjdk-21.tar.gz

# 设置Java环境变量
ENV JAVA_HOME=/usr/local/java
ENV PATH=$JAVA_HOME/bin:$PATH

RUN mkdir -p /judge/testcase /judge/testcase /judge/run /judge/spj /judge/log


# 设置构建参数
ARG SPRING_PROFILES_ACTIVE

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY  ./app/app-oj/target/*.jar /judge/server/app.jar

COPY ./app/app-oj/run.sh /judge/server/run.sh

COPY ./app/app-oj/testlib.h /usr/include/testlib.h

ADD ./app/app-oj/Sandbox-amd64 /judge/server/Sandbox-amd64
ADD ./app/app-oj/Sandbox-arm64 /judge/server/Sandbox-arm64
	
WORKDIR /judge/server

ENTRYPOINT ["bash", "./run.sh"]

EXPOSE 7101

EXPOSE 5050