package com.glowxq.wingman.transaction.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.transaction.pojo.po.Transaction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * Transaction导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@Schema(description = "Transaction导入DTO")
public class TransactionImportDTO implements BaseDTO {

    @ExcelProperty(value = "订单ID")
    @Schema(description = "订单ID")
    private Long orderId;

    @ExcelProperty(value = "订单号")
    @Schema(description = "订单号")
    private String orderNumber;

    @ExcelProperty(value = "流水号")
    @Schema(description = "流水号")
    private String transactionNumber;

    @ExcelProperty(value = "金额")
    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "交易时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    @ExcelProperty(value = "支出/收回款")
    @Schema(description = "支出/收回款")
    private String type;

    @ExcelProperty(value = "操作人")
    @Schema(description = "操作人")
    private String handleUser;

    @ExcelProperty(value = "专家用户ID")
    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @ExcelProperty(value = "专家姓名")
    @Schema(description = "专家姓名")
    private String expertName;

    @ExcelProperty(value = "专家联系方式")
    @Schema(description = "专家联系方式")
    private String expertContact;

    @ExcelProperty(value = "专家二维码")
    @Schema(description = "专家二维码")
    private String expertQrCode;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "启用状态")
    @Schema(description = "启用状态")
    private Boolean enable;

    @Override
    public Transaction buildEntity() {
        return BeanCopyUtils.copy(this, Transaction.class);
    }
}