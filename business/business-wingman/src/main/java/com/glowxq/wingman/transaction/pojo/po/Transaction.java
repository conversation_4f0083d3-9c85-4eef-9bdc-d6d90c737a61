package com.glowxq.wingman.transaction.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.wingman.transaction.enums.TransactionType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 流水
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@Table(value = "transaction", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "流水")
public class Transaction implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "流水ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "流水号")
    private String transactionNumber;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "交易时间")
    private LocalDateTime time;

    @Schema(description = "支出/收回款")
    private String type;

    @Schema(description = "操作人")
    private String handleUser;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;

    public TransactionType type() {
        return TransactionType.matchCode(type);
    }
}