package com.glowxq.wingman.transaction.enums;

import com.glowxq.core.common.enums.BaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum TransactionType implements BaseType {

    Payout("Payout", "支出"),
    Receive("Receive", "收回款"),

    ;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static TransactionType matchCode(String code) {
        for (TransactionType transactionType : TransactionType.values()) {
            if (transactionType.getCode().equals(code)) {
                return transactionType;
            }
        }
        return null;
    }
}
