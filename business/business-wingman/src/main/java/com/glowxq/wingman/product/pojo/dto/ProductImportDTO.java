package com.glowxq.wingman.product.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.product.pojo.po.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Product导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Product导入DTO")
public class ProductImportDTO implements BaseDTO {

    @ExcelProperty(value = "分类ID")
    @Schema(description = "分类ID")
    private Long categoryId;

    @ExcelProperty(value = "商品名")
    @Schema(description = "商品名")
    private String name;

    @Schema(description = "商品副标题")
    private String subName;

    @ExcelProperty(value = "价格")
    @Schema(description = "价格")
    private BigDecimal price;

    @ExcelProperty(value = "富文本介绍")
    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "介绍图片")
    private String contentImage;

    @ExcelProperty(value = "商品简介")
    @Schema(description = "商品简介")
    private String simpleContent;

    @ExcelProperty(value = "商品类型")
    @Schema(description = "商品类型")
    private String type;

    @ExcelProperty(value = "库存")
    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @ExcelProperty(value = "商品主图")
    @Schema(description = "商品主图")
    private String image;

    @ExcelProperty(value = "商品图集")
    @Schema(description = "商品图集")
    private String imageGallery;

    @ExcelProperty(value = "专家用户ID")
    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @ExcelProperty(value = "专家姓名")
    @Schema(description = "专家姓名")
    private String expertName;

    @ExcelProperty(value = "专家联系方式")
    @Schema(description = "专家联系方式")
    private String expertContact;

    @ExcelProperty(value = "专家跳转链接")
    @Schema(description = "专家跳转链接")
    private String expertUrl;

    @ExcelProperty(value = "专家二维码")
    @Schema(description = "专家二维码")
    private String expertQrCode;

    @ExcelProperty(value = "专家服务周期")
    @Schema(description = "专家服务周期")
    private String serviceTime;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private Boolean enable;

    @Override
    public Product buildEntity() {
        return BeanCopyUtils.copy(this, Product.class);
    }
}