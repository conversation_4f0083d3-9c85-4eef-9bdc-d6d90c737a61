package com.glowxq.wingman.transaction.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.wingman.transaction.pojo.dto.TransactionCreateDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionListDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionUpdateDTO;
import com.glowxq.wingman.transaction.pojo.vo.TransactionVO;
import com.glowxq.wingman.transaction.service.TransactionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * transaction/流水 Controller
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Tag(name = "流水")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class TransactionController extends BaseApi {

    private final TransactionService transactionService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "transaction.create")
    @PostMapping("/transaction/create")
    public ApiResult<Void> create(@RequestBody TransactionCreateDTO dto) {
        transactionService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "transaction.update")
    @PutMapping("/transaction/update")
    public ApiResult<Void> update(@RequestBody TransactionUpdateDTO dto) {
        transactionService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "transaction.remove")
    @DeleteMapping("/transaction/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        transactionService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "transaction.query_table")
    @GetMapping("/transaction/list")
    public ApiResult<PageResult<TransactionVO>> list(TransactionListDTO dto) {
        return ApiPageResult.success(transactionService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "transaction.query_table")
    @GetMapping("/transaction/detail")
    public ApiResult<TransactionVO> detail(@RequestParam Long id) {
        return ApiResult.success(transactionService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "transaction.import")
    @PostMapping("/transaction/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        transactionService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "transaction.export")
    @PostMapping("/transaction/export")
    public void exportExcel(@RequestBody TransactionListDTO dto, HttpServletResponse response) {
        transactionService.exportExcel(dto, response);
    }
}