package com.glowxq.wingman.teacher.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.system.meta.enums.TagBusinessBindType;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.glowxq.system.meta.service.MetaTagService;
import com.glowxq.wingman.teacher.mapper.TeacherMapper;
import com.glowxq.wingman.teacher.pojo.dto.TeacherCreateDTO;
import com.glowxq.wingman.teacher.pojo.dto.TeacherImportDTO;
import com.glowxq.wingman.teacher.pojo.dto.TeacherListDTO;
import com.glowxq.wingman.teacher.pojo.dto.TeacherUpdateDTO;
import com.glowxq.wingman.teacher.pojo.po.Teacher;
import com.glowxq.wingman.teacher.pojo.vo.TeacherVO;
import com.glowxq.wingman.teacher.service.TeacherService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 导师 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@RequiredArgsConstructor
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements TeacherService {

    private final MetaTagService metaTagService;
    private static QueryWrapper buildQueryWrapper(TeacherListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Teacher.class);
        wrapper.eq(Teacher::getCategoryId, dto.getCategoryId(), Utils.isNotNull(dto.getCategoryId()));
        wrapper.like(Teacher::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.like(Teacher::getSubName, dto.getSubName(), Utils.isNotNull(dto.getSubName()));
        wrapper.eq(Teacher::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        wrapper.eq(Teacher::getSimpleContent, dto.getSimpleContent(), Utils.isNotNull(dto.getSimpleContent()));
        wrapper.eq(Teacher::getContent, dto.getContent(), Utils.isNotNull(dto.getContent()));
        // wrapper.eq(Teacher::getType, dto.getType(), Utils.isNotNull(dto.getType()));
        wrapper.eq(Teacher::getInventory, dto.getInventory(), Utils.isNotNull(dto.getInventory()));
        wrapper.eq(Teacher::getQuantity, dto.getQuantity(), Utils.isNotNull(dto.getQuantity()));
        wrapper.eq(Teacher::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(Teacher::getContentImage, dto.getContentImage(), Utils.isNotNull(dto.getContentImage()));
        wrapper.eq(Teacher::getImageGallery, dto.getImageGallery(), Utils.isNotNull(dto.getImageGallery()));
        wrapper.eq(Teacher::getExpertUserId, dto.getExpertUserId(), Utils.isNotNull(dto.getExpertUserId()));
        wrapper.like(Teacher::getExpertName, dto.getExpertName(), Utils.isNotNull(dto.getExpertName()));
        wrapper.eq(Teacher::getExpertContact, dto.getExpertContact(), Utils.isNotNull(dto.getExpertContact()));
        wrapper.eq(Teacher::getExpertUrl, dto.getExpertUrl(), Utils.isNotNull(dto.getExpertUrl()));
        wrapper.eq(Teacher::getExpertQrCode, dto.getExpertQrCode(), Utils.isNotNull(dto.getExpertQrCode()));
        wrapper.eq(Teacher::getServiceTime, dto.getServiceTime(), Utils.isNotNull(dto.getServiceTime()));
        wrapper.eq(Teacher::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        wrapper.eq(Teacher::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.orderBy(Teacher::getSort).desc();
        return wrapper;
    }

    @Override
    public void create(TeacherCreateDTO dto) {
        Teacher teacher = BeanCopyUtils.copy(dto, Teacher.class);
        save(teacher);
    }

    @Override
    public void update(TeacherUpdateDTO dto) {
        Teacher teacher = BeanCopyUtils.copy(dto, Teacher.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(Teacher::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(teacher);
        metaTagService.unBindAll(teacher.getId(), TagBusinessBindType.Product);
        metaTagService.bindTags(teacher.getId(), dto.getTagIds(), TagBusinessBindType.Product);
    }

    @Override
    public PageResult<TeacherVO> page(TeacherListDTO dto) {
        List<Long> productIds = metaTagService.listBusinessIdByTagIds(dto.getTagIds(), TagBusinessBindType.Product);
        QueryWrapper queryWrapper = buildQueryWrapper(dto);
        queryWrapper.in(Teacher::getId, productIds, CollectionUtils.isNotEmpty(productIds));
        Page<TeacherVO> page = pageAs(PageUtils.getPage(dto), queryWrapper, TeacherVO.class);

        List<TeacherVO> techerVoList = page.getRecords();
        techerVoList.forEach(item -> {
            item.setTagList(List.of());
        });
        page.setRecords(techerVoList);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<TeacherVO> list(TeacherListDTO dto) {
        return listAs(buildQueryWrapper(dto), TeacherVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public TeacherVO detail(Long id) {
        Teacher teacher = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(teacher);
        List<MetaTag> metaTags = metaTagService.listByBusinessId(id, TagBusinessBindType.Product);
        TeacherVO teacherVO = BeanCopyUtils.copy(teacher, TeacherVO.class);
        teacherVO.setTagList(metaTags);
        return teacherVO;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<TeacherImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), TeacherImportDTO.class, true);
        List<TeacherImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(TeacherListDTO dto, HttpServletResponse response) {
        List<TeacherVO> list = list(dto);
        String fileName = "导师模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "导师", TeacherVO.class, os);
    }
}