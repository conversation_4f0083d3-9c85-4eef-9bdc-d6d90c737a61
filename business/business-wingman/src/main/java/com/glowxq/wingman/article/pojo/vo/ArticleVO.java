package com.glowxq.wingman.article.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.glowxq.system.meta.pojo.po.MetaTag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Article返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Article返回vo")
public class ArticleVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "商品ID")
    private Long id;

    @ExcelProperty(value = "案例用户")
    @Schema(description = "案例用户")
    private Long userId;

    @ExcelProperty(value = "案例专家")
    @Schema(description = "案例专家")
    private Long expertUserId;

    @ExcelProperty(value = "分类ID")
    @Schema(description = "分类ID")
    private Long categoryId;

    @ExcelProperty(value = "案例人")
    @Schema(description = "案例人")
    private String articleUser;

    @ExcelProperty(value = "案例标题")
    @Schema(description = "案例标题")
    private String title;

    @ExcelProperty(value = "价格")
    @Schema(description = "价格")
    private BigDecimal price;

    @ExcelProperty(value = "富文本介绍")
    @Schema(description = "富文本介绍")
    private String content;


    @ExcelProperty(value = "成果摘要")
    @Schema(description = "成果摘要")
    private String achievement;

    @ExcelProperty(value = "案例主图")
    @Schema(description = "案例主图")
    private String image;

    @ExcelProperty(value = "案例图集")
    @Schema(description = "案例图集")
    private String imageGallery;

    @ExcelProperty(value = "专家联系方式")
    @Schema(description = "专家联系方式")
    private String expertContact;

    @ExcelProperty(value = "专家二维码")
    @Schema(description = "专家二维码")
    private String expertQrCode;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "标签标签")
    private List<MetaTag> tagList;
}