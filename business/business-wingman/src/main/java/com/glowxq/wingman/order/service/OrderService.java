package com.glowxq.wingman.order.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.wechat.pay.pojo.WechatPaymentData;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.dto.*;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.pojo.vo.OrderVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 订单 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface OrderService extends IService<Order> {

    void create(OrderCreateDTO dto);

    void update(OrderUpdateDTO dto);

    PageResult<OrderVO> page(OrderListDTO dto);

    List<OrderVO> list(OrderListDTO dto);

    void remove(SelectIdsDTO dto);

    OrderVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(OrderListDTO dto, HttpServletResponse response);

    Order create(ClientOrderCreateDTO dto);

    void update(ClientOrderUpdateDTO dto);

    Order getByOrderNumber(String orderNumber);

    WechatPaymentData payment(Long orderId);

    List<Order> listByStatus(OrderStatus orderStatus);
}