package com.glowxq.wingman.transaction.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryCreateDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryListDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryUpdateDTO;
import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import com.glowxq.wingman.transaction.pojo.vo.TransactionSummaryVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 流水报告 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface TransactionSummaryService extends IService<TransactionSummary> {

    void create(TransactionSummaryCreateDTO dto);

    void update(TransactionSummaryUpdateDTO dto);

    PageResult<TransactionSummaryVO> page(TransactionSummaryListDTO dto);

    List<TransactionSummaryVO> list(TransactionSummaryListDTO dto);

    void remove(SelectIdsDTO dto);

    TransactionSummaryVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(TransactionSummaryListDTO dto, HttpServletResponse response);

    TransactionSummary getByOrderNumber(String orderNumber);
}