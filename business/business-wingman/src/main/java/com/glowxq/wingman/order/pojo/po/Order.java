package com.glowxq.wingman.order.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.product.enums.ProductType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Table(value = "order", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "订单")
public class Order implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "下单用户")
    private Long userId;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "小程序openid")
    private String openid;

    @Schema(description = "产品名")
    private String productName;

    @Schema(description = "商品类型")
    private String productType;

    @Schema(description = "支付平台交易号")
    private String transactionId;

    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "下单用户姓名")
    private String orderUser;

    @Schema(description = "下单用户手机号")
    private String orderPhone;

    @Schema(description = "商品名")
    private String name;

    @Schema(description = "绑定分销码")
    private String bindCode;

    @Schema(description = "分销用户ID")
    private Long bindUserId;

    @Schema(description = "分销用户名")
    private String bindName;

    @Schema(description = "分销商电话")
    private String bindPhone;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "下单数量")
    @Min(value = 1, message = "商品数量至少为1")
    private Integer quantity;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "订单类型")
    private String type;

    @Schema(description = "库存")
    private Integer inventory;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "商品图集")
    private String imageGallery;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "专家服务周期")
    private String serviceTime;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;

    public OrderStatus status() {
        return OrderStatus.matchCode(status);
    }

    public ProductType productType() {
        return ProductType.matchCode(productType);
    }
}