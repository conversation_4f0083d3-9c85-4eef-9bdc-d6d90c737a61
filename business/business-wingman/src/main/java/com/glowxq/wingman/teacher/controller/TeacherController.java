package com.glowxq.wingman.teacher.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.wingman.teacher.pojo.dto.TeacherCreateDTO;
import com.glowxq.wingman.teacher.pojo.dto.TeacherListDTO;
import com.glowxq.wingman.teacher.pojo.dto.TeacherUpdateDTO;
import com.glowxq.wingman.teacher.pojo.vo.TeacherVO;
import com.glowxq.wingman.teacher.service.TeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * teacher/导师 Api
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Tag(name = "导师")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class TeacherController extends BaseApi {

    private final TeacherService teacherService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "teacher.create")
    @PostMapping("/teacher/create")
    public ApiResult<Void> create(@RequestBody TeacherCreateDTO dto) {
        teacherService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "teacher.update")
    @PutMapping("/teacher/update")
    public ApiResult<Void> update(@RequestBody TeacherUpdateDTO dto) {
        teacherService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "teacher.remove")
    @DeleteMapping("/teacher/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        teacherService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "teacher.query_table")
    @GetMapping("/teacher/list")
    public ApiResult<PageResult<TeacherVO>> list(TeacherListDTO dto) {
        return ApiPageResult.success(teacherService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "teacher.query_table")
    @GetMapping("/teacher/detail")
    public ApiResult<TeacherVO> detail(@RequestParam Long id) {
        return ApiResult.success(teacherService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "teacher.import")
    @PostMapping("/teacher/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        teacherService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "teacher.export")
    @PostMapping("/teacher/export")
    public void exportExcel(@RequestBody TeacherListDTO dto, HttpServletResponse response) {
        teacherService.exportExcel(dto, response);
    }
}