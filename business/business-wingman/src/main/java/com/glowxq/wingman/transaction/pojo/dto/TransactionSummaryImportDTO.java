package com.glowxq.wingman.transaction.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * TransactionSummary导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@Schema(description = "TransactionSummary导入DTO")
public class TransactionSummaryImportDTO implements BaseDTO {

    @ExcelProperty(value = "订单ID")
    @Schema(description = "订单ID")
    private Long orderId;

    @ExcelProperty(value = "订单号")
    @Schema(description = "订单号")
    private String orderNumber;

    @ExcelProperty(value = "流水号")
    @Schema(description = "流水号")
    private String transactionNumber;

    @ExcelProperty(value = "订单金额")
    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty(value = "支出金额")
    @Schema(description = "支出金额")
    private BigDecimal payoutAmount;

    @ExcelProperty(value = "退还金额")
    @Schema(description = "退还金额")
    private BigDecimal receiveAmount;

    @ExcelProperty(value = "利润金额")
    @Schema(description = "利润金额")
    private BigDecimal amount;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "启用状态")
    @Schema(description = "启用状态")
    private Boolean enable;

    @Override
    public TransactionSummary buildEntity() {
        return BeanCopyUtils.copy(this, TransactionSummary.class);
    }
}