<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.wingman.article.mapper.ArticleMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.wingman.article.pojo.po.Article">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="expert_user_id" property="expertUserId"/>
        <result column="category_id" property="categoryId"/>
        <result column="article_user" property="articleUser"/>
        <result column="title" property="title"/>
        <result column="price" property="price"/>
        <result column="content" property="content"/>
        <result column="image" property="image"/>
        <result column="image_gallery" property="imageGallery"/>
        <result column="expert_contact" property="expertContact"/>
        <result column="expert_qr_code" property="expertQrCode"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, expert_user_id, category_id, article_user, title, price, content, image, image_gallery, expert_contact, expert_qr_code, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
