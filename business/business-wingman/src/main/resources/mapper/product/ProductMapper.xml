<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.wingman.product.mapper.ProductMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.wingman.product.pojo.po.Product">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="name" property="name"/>
        <result column="price" property="price"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="inventory" property="inventory"/>
        <result column="image" property="image"/>
        <result column="image_gallery" property="imageGallery"/>
        <result column="expert_user_id" property="expertUserId"/>
        <result column="expert_name" property="expertName"/>
        <result column="expert_contact" property="expertContact"/>
        <result column="expert_url" property="expertUrl"/>
        <result column="expert_qr_code" property="expertQrCode"/>
        <result column="service_time" property="serviceTime"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_id, name, price, content, type, inventory, image, image_gallery, expert_user_id, expert_name, expert_contact, expert_url, expert_qr_code, service_time, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
