package com.glowxq.system.meta.enums;

import com.glowxq.core.common.enums.BaseType;
import com.glowxq.system.meta.base.BusinessBindEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum TagBusinessBindType implements BaseType, BusinessBindEnum {

    // common
    User("User", "用户"),

    // oj
    Work("Work", "作业"),
    Group("Group", "班级"),
    Topic("Topic", "主题"),
    Course("Course", "课程"),
    Problem("Problem", "题目"),
    Contest("Contest", "比赛"),

    // wingman
    Product("Product", "商品"),
    Article("Article", "案例"),

    // rescue

    ;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static TagBusinessBindType matchCode(String code) {
        for (TagBusinessBindType pushStatus : TagBusinessBindType.values()) {
            if (pushStatus.getCode().equals(code)) {
                return pushStatus;
            }
        }
        return null;
    }
}
