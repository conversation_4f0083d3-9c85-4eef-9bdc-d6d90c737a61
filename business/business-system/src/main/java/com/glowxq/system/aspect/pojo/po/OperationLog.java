package com.glowxq.system.aspect.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志 pojo
 * <AUTHOR>
 * @description 操作日志
 * @classname OperationLog
 * @date 2025/5/27 20:46
 */
@Data
@Table(value = "OPERATION_LOG", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "操作日志")
public class OperationLog implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id(keyType = KeyType.Auto)
    @Schema(description = "ID")
    private Long id;
    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String username;
    /**
     * ip
     */
    @Schema(description = "ip")
    private String ip;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;
    /**
     * 方法
     */
    @Schema(description = "方法")
    private String method;
    /**
     * 访问URL
     */
    @Schema(description = "访问URL")
    private String url;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String content;
    /**
     * 模块code
     */
    @Schema(description = "模块code")
    private String module;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createId;
    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateId;
}
