package com.glowxq.system.aspect.controller;

import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.system.aspect.pojo.dto.OperationLogDTO;
import com.glowxq.system.aspect.pojo.vo.OperationLogVO;
import com.glowxq.system.aspect.service.OperationLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/10 22:30
 */
@Tag(name =  "操作日志")
@RestController
@RequestMapping("/operLog")
@RequiredArgsConstructor
public class OperationLogController extends BaseApi {
    private final OperationLogService operationLogService;

    /**
     * 根据入参查询操作日志并分页
     * @param operationLogDTO 入参
     * @return 操作日志
     */
    @GetMapping("/list")
    public ApiResult<PageResult<OperationLogVO>> list(@RequestBody OperationLogDTO operationLogDTO) {
        PageResult<OperationLogVO> page = operationLogService.page(operationLogDTO);
        return ApiResult.success(page);
    }
}
