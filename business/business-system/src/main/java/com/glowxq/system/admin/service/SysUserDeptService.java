package com.glowxq.system.admin.service;

import com.mybatisflex.core.service.IService;
import com.glowxq.system.admin.pojo.dto.sysuser.UserDeptDTO;
import com.glowxq.system.admin.pojo.po.SysUserDept;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户-部门关系表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
public interface SysUserDeptService extends IService<SysUserDept> {

    @Transactional
    void bind(UserDeptDTO dto);

    void unbind(List<Long> userIds);
}