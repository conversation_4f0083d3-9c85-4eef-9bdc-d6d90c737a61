package com.glowxq.oj.group.service.helper;

import com.glowxq.oj.system.OjUserInfoService;
import com.glowxq.oj.system.bo.OjUserInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GroupDataPackHelper {

    private final OjUserInfoService ojUserInfoService;

    public List<OjUserInfo> listUserInfoByGroupId(Long groupId) {
        return ojUserInfoService.listByGroupId(groupId);
    }
}
