package com.glowxq.oj.user.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.oj.group.service.GroupService;
import com.glowxq.oj.user.mapper.UserInfoMapper;
import com.glowxq.oj.user.pojo.dto.*;
import com.glowxq.oj.user.pojo.po.UserInfo;
import com.glowxq.oj.user.pojo.vo.UserInfoVO;
import com.glowxq.oj.user.service.UserInfoService;
import com.glowxq.system.meta.enums.TagBusinessBindType;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户OJ信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    private final GroupService groupService;

    private static QueryWrapper buildQueryWrapper(UserInfoListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(UserInfo.class);
        wrapper.eq(UserInfo::getUserId, dto.getUserId(), Utils.isNotNull(dto.getUserId()));
        wrapper.like(UserInfo::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(UserInfo::getAcNum, dto.getAcNum(), Utils.isNotNull(dto.getAcNum()));
        wrapper.eq(UserInfo::getIntegral, dto.getIntegral(), Utils.isNotNull(dto.getIntegral()));
        wrapper.eq(UserInfo::getContinuousSignDay, dto.getContinuousSignDay(), Utils.isNotNull(dto.getContinuousSignDay()));
        wrapper.eq(UserInfo::getSubmitNum, dto.getSubmitNum(), Utils.isNotNull(dto.getSubmitNum()));
        wrapper.eq(UserInfo::getTitle, dto.getTitle(), Utils.isNotNull(dto.getTitle()));
        wrapper.eq(UserInfo::getColor, dto.getColor(), Utils.isNotNull(dto.getColor()));
        return wrapper;
    }

    @Override
    public void create(UserInfoCreateDTO dto) {
        UserInfo userInfo = BeanCopyUtils.copy(dto, UserInfo.class);
        save(userInfo);
    }

    @Override
    public void update(UserInfoUpdateDTO dto) {
        UserInfo userInfo = BeanCopyUtils.copy(dto, UserInfo.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(UserInfo::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(userInfo);
    }

    @Override
    public PageResult<UserInfoVO> page(UserInfoListDTO dto) {
        Page<UserInfoVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), UserInfoVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<UserInfoVO> list(UserInfoListDTO dto) {
        return listAs(buildQueryWrapper(dto), UserInfoVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public UserInfoVO detail(Object id) {
        UserInfo userInfo = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(userInfo);
        return BeanCopyUtils.copy(userInfo, UserInfoVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<UserInfoImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), UserInfoImportDTO.class, true);
        List<UserInfoImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(UserInfoListDTO dto, HttpServletResponse response) {
        List<UserInfoVO> list = list(dto);
        String fileName = "用户OJ信息模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "用户OJ信息", UserInfoVO.class, os);
    }

    @Override
    public UserInfo autoByUserId(Long userId, String tenantId) {
        UserInfo userInfo = mapper.getByUserId(userId);
        if (userInfo != null) {
            return userInfo;
        }
        userInfo = new UserInfo();
        userInfo.setUserId(userId);
        userInfo.setTenantId(tenantId);
        mapper.insert(userInfo, true);
        return userInfo;
    }

    @Override
    public List<UserInfo> autoListByUserIds(List<Long> userIds) {
        List<UserInfo> userInfos = mapper.listByUserIds(userIds);
        //     检查哪个userid的数据不存在，如果不存在则马上增加保存到数据库中
        List<Long> exitUserIds = userInfos.stream().map(UserInfo::getUserId).toList();
        List<Long> notInUserIds = ListUtils.removeAll(userIds, exitUserIds);
        if (CollectionUtils.isNotEmpty(notInUserIds)) {
            List<UserInfo> newUserInfos = notInUserIds.stream().map(userId -> {
                UserInfo userInfo = new UserInfo();
                userInfo.setUserId(userId);
                return userInfo;
            }).toList();
            saveBatch(newUserInfos);
            userInfos.addAll(newUserInfos);
        }
        return userInfos;
    }

    @Override
    public List<UserInfo> acRankingList(RankingDTO rankingDTO) {
        List<Long> userIds = new ArrayList<>();
        if (rankingDTO.getGroupId() != null) {
            userIds = groupService.listBusinessIdByGroupId(rankingDTO.getGroupId(), TagBusinessBindType.User);
        }
        return mapper.acRankingList(userIds);
    }

    @Override
    public List<UserInfo> acRankingList() {
        return mapper.acRankingList();
    }
}