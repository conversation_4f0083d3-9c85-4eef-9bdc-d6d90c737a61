package com.glowxq.oj.judge.processor;

import com.glowxq.oj.judge.processor.handler.JudgeHandler;
import com.glowxq.oj.judge.pojo.po.Judge;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/1
 */
@Component
@RequiredArgsConstructor
public class FixedJudgeProcessor extends BaseJudgeProcessor {

    private final JudgeHandler judgeHandler;

    @Override
    protected Judge doJudge(Judge judge) {
        return judgeHandler.judgeFixed(judge);
    }
}
