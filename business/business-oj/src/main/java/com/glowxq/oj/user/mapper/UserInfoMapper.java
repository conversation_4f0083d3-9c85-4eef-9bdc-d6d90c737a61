package com.glowxq.oj.user.mapper;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.glowxq.oj.user.pojo.po.UserInfo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 用户OJ信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    default UserInfo getByUserId(Long userId) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.from(UserInfo.class);
        queryWrapper.eq(UserInfo::getUserId, userId);
        return this.selectOneByQuery(queryWrapper);
    }

    default List<UserInfo> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.from(UserInfo.class);
        queryWrapper.in(UserInfo::getUserId, userIds);
        return this.selectListByQuery(queryWrapper);
    }

    default List<UserInfo> acRankingList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(UserInfo.class);
        queryWrapper.in(UserInfo::getUserId, userIds);
        queryWrapper.orderBy(UserInfo::getAcNum).desc();
        return this.selectListByQuery(queryWrapper);
    }

    default List<UserInfo> acRankingList() {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(UserInfo.class);
        queryWrapper.orderBy(UserInfo::getAcNum).desc();
        queryWrapper.limit(10);
        return this.selectListByQuery(queryWrapper);
    }
}