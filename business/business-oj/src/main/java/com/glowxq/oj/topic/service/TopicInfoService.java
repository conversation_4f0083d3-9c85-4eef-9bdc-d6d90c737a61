package com.glowxq.oj.topic.service;

import com.mybatisflex.core.service.IService;
import com.glowxq.oj.judge.pojo.po.Judge;
import com.glowxq.oj.system.bo.OjUserInfo;
import com.glowxq.oj.topic.pojo.dto.TopicRankDTO;
import com.glowxq.oj.topic.pojo.po.TopicInfo;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.entity.PageResult;
import java.util.List;
import com.glowxq.oj.topic.pojo.dto.TopicInfoCreateDTO;
import com.glowxq.oj.topic.pojo.dto.TopicInfoUpdateDTO;
import com.glowxq.oj.topic.pojo.dto.TopicInfoListDTO;
import com.glowxq.oj.topic.pojo.po.TopicSubmit;
import com.glowxq.oj.topic.pojo.vo.TopicInfoVO;
import com.glowxq.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 主题数据 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
public interface TopicInfoService extends IService<TopicInfo> {

    void create(TopicInfoCreateDTO dto);

    void update(TopicInfoUpdateDTO dto);

    PageResult<TopicInfoVO> page(TopicInfoListDTO dto);

    List<TopicInfoVO> list(TopicInfoListDTO dto);

    void remove(SelectIdsDTO dto);

    TopicInfoVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(TopicInfoListDTO dto, HttpServletResponse response);

    void autoJudge(Judge judge, TopicSubmit topicSubmit, OjUserInfo userInfo);

    TopicInfo autoTopicId(Long topicId, OjUserInfo userInfo);

    List<TopicInfo> topicRank(TopicRankDTO dto);
}