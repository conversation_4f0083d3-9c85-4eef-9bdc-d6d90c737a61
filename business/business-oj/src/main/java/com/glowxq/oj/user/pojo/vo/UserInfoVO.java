package com.glowxq.oj.user.pojo.vo;

import com.glowxq.core.common.entity.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;

/**
 * <p>
 * UserInfo返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Data
@Schema(description = "UserInfo返回vo")
public class UserInfoVO implements BaseVO{

    @ExcelIgnore
    @Schema(description =  "")
    private Long id;

    @ExcelProperty(value = "用户ID")
    @Schema(description =  "用户ID")
    private Long userId;

    @ExcelProperty(value = "姓名")
    @Schema(description =  "姓名")
    private String name;

    @ExcelProperty(value = "ac数量")
    @Schema(description =  "ac数量")
    private Integer acNum;

    @ExcelProperty(value = "用户积分")
    @Schema(description =  "用户积分")
    private Integer integral;

    @ExcelProperty(value = "连续签到时间")
    @Schema(description =  "连续签到时间")
    private Integer continuousSignDay;

    @ExcelProperty(value = "提交题目数量")
    @Schema(description =  "提交题目数量")
    private Integer submitNum;

    @ExcelProperty(value = "称号")
    @Schema(description =  "称号")
    private String title;

    @ExcelProperty(value = "颜色")
    @Schema(description =  "颜色")
    private String color;

}