package com.glowxq.oj.user.pojo.dto;

import com.glowxq.oj.user.pojo.po.UserInfo;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.glowxq.core.common.entity.PageQuery;

/**
 * <p>
 * UserInfo查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Data
@Schema(description = "UserInfo查询DTO")
public class UserInfoListDTO extends PageQuery implements BaseDTO {

    @Schema(description =  "用户ID")
    private Long userId;

    @Schema(description =  "姓名")
    private String name;

    @Schema(description =  "ac数量")
    private Integer acNum;

    @Schema(description =  "用户积分")
    private Integer integral;

    @Schema(description =  "连续签到时间")
    private Integer continuousSignDay;

    @Schema(description =  "提交题目数量")
    private Integer submitNum;

    @Schema(description =  "称号")
    private String title;

    @Schema(description =  "颜色")
    private String color;

    @Override
    public UserInfo buildEntity() {
        return BeanCopyUtils.copy(this, UserInfo.class);
    }
}