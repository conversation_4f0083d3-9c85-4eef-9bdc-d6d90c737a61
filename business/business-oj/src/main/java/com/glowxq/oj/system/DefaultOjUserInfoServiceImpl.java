package com.glowxq.oj.system;

import com.glowxq.oj.system.bo.OjUserInfo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/2
 */
@Component
public class DefaultOjUserInfoServiceImpl implements OjUserInfoService {

    @Override
    public List<OjUserInfo> listByGroupId(Long groupId) {
        return null;
    }

    @Override
    public OjUserInfo getByUserId(Long userId) {
        return null;
    }
}
