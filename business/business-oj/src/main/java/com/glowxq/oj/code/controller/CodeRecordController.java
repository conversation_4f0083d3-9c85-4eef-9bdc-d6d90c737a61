package com.glowxq.oj.code.controller;

import com.glowxq.core.common.annotation.LogRecord;
import com.glowxq.core.common.enums.ModueEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.oj.code.service.CodeRecordService;
import com.glowxq.oj.code.pojo.dto.CodeRecordCreateDTO;
import com.glowxq.oj.code.pojo.dto.CodeRecordUpdateDTO;
import com.glowxq.oj.code.pojo.dto.CodeRecordListDTO;
import com.glowxq.oj.code.pojo.vo.CodeRecordVO;
import com.glowxq.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * code/用户代码 Api
 *
 * <AUTHOR>
 * @since 2025-04-04
 */
@Tag(name =  "用户代码")
@RestController
@RequestMapping("/code-record")
@RequiredArgsConstructor
public class CodeRecordController extends BaseApi  {

    private final CodeRecordService codeRecordService;

    @Operation(summary = "新增代码")
    @SaCheckPermission(value = "code.record.create")
    @PostMapping
    @LogRecord(module = ModueEnum.CODE)
    public ApiResult<Void> create(@RequestBody CodeRecordCreateDTO dto) {
        codeRecordService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改代码")
    @SaCheckPermission(value = "code.record.update")
    @PutMapping
    @LogRecord(module = ModueEnum.CODE)
    public ApiResult<Void> update(@RequestBody CodeRecordUpdateDTO dto) {
        codeRecordService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除代码")
    @SaCheckPermission(value = "code.record.remove")
    @DeleteMapping
    @LogRecord(module = ModueEnum.CODE)
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        codeRecordService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "code.record.query_table")
    @GetMapping
    @LogRecord(module = ModueEnum.CODE)
    public ApiResult<PageResult<CodeRecordVO>> list(CodeRecordListDTO dto) {
        return ApiPageResult.success(codeRecordService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "code.record.query_table")
    @GetMapping("/{id}")
    @LogRecord(module = ModueEnum.CODE)
    public ApiResult<CodeRecordVO> detail(@PathVariable Object id) {
        return ApiResult.success(codeRecordService.detail(id));
    }

    @Operation(summary = "导入代码")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "code.record.import")
    @PostMapping("/import")
    @LogRecord(module = ModueEnum.CODE)
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        codeRecordService.importExcel(dto);
    }

    @Operation(summary = "导出代码")
    @SaCheckPermission(value = "code.record.export")
    @PostMapping("/export")
    @LogRecord(module = ModueEnum.CODE)
    public void exportExcel(@RequestBody CodeRecordListDTO dto, HttpServletResponse response) {
        codeRecordService.exportExcel(dto, response);
    }
}