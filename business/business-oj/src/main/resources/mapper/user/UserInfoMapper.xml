<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.user.mapper.UserInfoMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.user.pojo.po.UserInfo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="ac_num" property="acNum"/>
        <result column="integral" property="integral"/>
        <result column="continuous_sign_day" property="continuousSignDay"/>
        <result column="submit_num" property="submitNum"/>
        <result column="title" property="title"/>
        <result column="color" property="color"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, name, ac_num, integral, continuous_sign_day, submit_num, title, color, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
