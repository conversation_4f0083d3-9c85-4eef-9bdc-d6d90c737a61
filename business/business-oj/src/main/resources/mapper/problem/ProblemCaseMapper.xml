<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.problem.mapper.ProblemCaseMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.problem.pojo.po.ProblemCase">
        <id column="id" property="id"/>
        <result column="problem_id" property="problemId"/>
        <result column="problem_key" property="problemKey"/>
        <result column="input" property="input"/>
        <result column="output" property="output"/>
        <result column="input_url" property="inputUrl"/>
        <result column="output_url" property="outputUrl"/>
        <result column="type" property="type"/>
        <result column="score" property="score"/>
        <result column="enable" property="enable"/>
        <result column="group_num" property="groupNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, problem_id, problem_key, input, output, input_url, output_url, type, score, enable, group_num, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
